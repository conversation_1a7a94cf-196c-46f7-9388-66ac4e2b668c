import { respData, respErr } from "@/lib/resp";
import { getCurrentUserAdminInfo } from "@/services/admin";

export async function POST(req: Request) {
  try {
    const adminInfo = await getCurrentUserAdminInfo();
    
    if (!adminInfo) {
      return respErr("用户未登录", 401);
    }

    return respData({
      isAdmin: adminInfo.isAdmin,
      user: adminInfo.user,
    });
  } catch (e) {
    console.log("check admin failed: ", e);
    return respErr("检查管理员权限失败");
  }
}
