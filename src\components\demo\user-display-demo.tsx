"use client";

import * as React from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { ChevronDown } from "lucide-react";

// 模拟用户数据用于演示
const mockUsers = [
  {
    nickname: "<PERSON>",
    email: "<EMAIL>",
    avatar_url: "https://github.com/shadcn.png"
  },
  {
    nickname: "<PERSON>",
    email: "<EMAIL>", 
    avatar_url: "https://github.com/vercel.png"
  },
  {
    nickname: "Very Long Username That Should Be Truncated",
    email: "<EMAIL>",
    avatar_url: ""
  },
  {
    nickname: "张三李四王五赵六",
    email: "<EMAIL>",
    avatar_url: ""
  }
];

function UserDisplayDemo({ user }: { user: typeof mockUsers[0] }) {
  const dropdownItems = [
    { title: user.nickname },
    { title: "User Center", url: "/i/user-center" },
    { title: "Admin System", url: "/admin" },
    { title: "Contact", url: "/contact" },
    { title: "Sign Out", onClick: () => console.log("Sign out") },
  ];

  return (
    <TooltipProvider>
      <DropdownMenu>
        <Tooltip>
          <TooltipTrigger asChild>
            <DropdownMenuTrigger asChild>
              <div className="flex items-center gap-2 cursor-pointer hover:bg-accent hover:text-accent-foreground rounded-md px-2 py-1 transition-colors">
                <Avatar className="h-8 w-8 shrink-0">
                  <AvatarImage src={user.avatar_url} alt={user.nickname} />
                  <AvatarFallback className="text-xs font-medium">
                    {user.nickname?.slice(0, 2).toUpperCase() || 'UN'}
                  </AvatarFallback>
                </Avatar>
                <div className="hidden sm:flex items-center gap-1 min-w-0">
                  <span className="text-sm font-medium truncate max-w-[100px] md:max-w-[120px] lg:max-w-[140px]">
                    {user.nickname}
                  </span>
                  <ChevronDown className="h-3 w-3 opacity-50 shrink-0" />
                </div>
                {/* 移动端只显示头像，悬停时显示下拉箭头 */}
                <div className="sm:hidden">
                  <ChevronDown className="h-3 w-3 opacity-50" />
                </div>
              </div>
            </DropdownMenuTrigger>
          </TooltipTrigger>
          <TooltipContent>
            <p>{user.nickname}</p>
          </TooltipContent>
        </Tooltip>
      <DropdownMenuContent className="mx-4 bg-background">
        {dropdownItems.map((item, index) => (
          <React.Fragment key={index}>
            <DropdownMenuItem
              key={index}
              className="flex justify-center cursor-pointer"
            >
              {item.url ? (
                <a href={item.url} target="_blank" rel="noopener noreferrer">
                  {item.title}
                </a>
              ) : (
                <button onClick={item.onClick}>{item.title}</button>
              )}
            </DropdownMenuItem>
            {index !== dropdownItems.length - 1 && <DropdownMenuSeparator />}
          </React.Fragment>
        ))}
      </DropdownMenuContent>
      </DropdownMenu>
    </TooltipProvider>
  );
}

export default function UserDisplayDemoPage() {
  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        <div>
          <h1 className="text-3xl font-bold mb-4">用户名显示优化演示</h1>
          <p className="text-muted-foreground mb-8">
            展示页面右上角用户名显示的优化效果，包括长用户名截断、响应式设计和悬停提示。
          </p>
        </div>

        <div className="space-y-6">
          <h2 className="text-2xl font-semibold">不同用户名长度的显示效果</h2>
          
          {mockUsers.map((user, index) => (
            <div key={index} className="border rounded-lg p-6 space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium">用户 {index + 1}</h3>
                  <p className="text-sm text-muted-foreground">昵称: {user.nickname}</p>
                  <p className="text-sm text-muted-foreground">邮箱: {user.email}</p>
                </div>
                <div className="flex items-center gap-4">
                  <span className="text-sm text-muted-foreground">右上角显示效果:</span>
                  <UserDisplayDemo user={user} />
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="space-y-4">
          <h2 className="text-2xl font-semibold">优化特性</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="border rounded-lg p-4">
              <h3 className="font-medium mb-2">📱 响应式设计</h3>
              <p className="text-sm text-muted-foreground">
                在小屏幕设备上只显示头像和下拉箭头，在大屏幕上显示完整的用户名。
              </p>
            </div>
            <div className="border rounded-lg p-4">
              <h3 className="font-medium mb-2">✂️ 智能截断</h3>
              <p className="text-sm text-muted-foreground">
                长用户名会被自动截断，并通过 tooltip 显示完整内容。
              </p>
            </div>
            <div className="border rounded-lg p-4">
              <h3 className="font-medium mb-2">🎨 视觉优化</h3>
              <p className="text-sm text-muted-foreground">
                添加了悬停效果、圆角边框和平滑过渡动画。
              </p>
            </div>
            <div className="border rounded-lg p-4">
              <h3 className="font-medium mb-2">🔤 头像回退</h3>
              <p className="text-sm text-muted-foreground">
                当头像加载失败时，显示用户名的前两个字符作为回退。
              </p>
            </div>
          </div>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h3 className="font-medium text-blue-900 mb-2">💡 使用说明</h3>
          <p className="text-sm text-blue-800">
            请尝试调整浏览器窗口大小来查看响应式效果，将鼠标悬停在用户名上查看完整内容的 tooltip。
          </p>
        </div>
      </div>
    </div>
  );
}
