'use client';

import { useTranslations } from 'next-intl';
import { Link } from '@/i18n/navigation';
import { Button } from '@/components/ui/button';

export default function NotFound() {
  const t = useTranslations('error');

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex flex-col items-center justify-center px-4">
      <div className="text-center">
        {/* 404 图标 */}
        <div className="mb-8">
          <div className="text-8xl font-bold text-white/20 mb-4">404</div>
          <div className="text-6xl mb-4">🔍</div>
        </div>

        {/* 错误信息 */}
        <h1 className="text-4xl font-bold text-white mb-4">
          {t('title')}
        </h1>
        <p className="text-xl text-gray-300 mb-8 max-w-md">
          {t('description')}
        </p>

        {/* 操作按钮 */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button asChild className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700">
            <Link href="/">
              {t('back_home')}
            </Link>
          </Button>
          <Button asChild variant="outline" className="border-white/20 text-white hover:bg-white/10">
            <Link href="/pricing">
              {t('view_pricing')}
            </Link>
          </Button>
        </div>

        {/* 帮助链接 */}
        <div className="mt-12 text-gray-400">
          <p className="mb-4">{t('need_help')}</p>
          <div className="flex justify-center gap-6 text-sm">
            <Link href="/privacy-policy" className="hover:text-white transition-colors">
              {t('privacy_policy')}
            </Link>
            <Link href="/terms-of-service" className="hover:text-white transition-colors">
              {t('terms_of_service')}
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
