import { respData, respErr } from "@/lib/resp";
import { getCreditsByUserUuid } from "@/models/credit";
import { getUserUuid } from "@/services/user";

export async function POST(req: Request) {
  try {
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("no auth");
    }

    const { page = 1, limit = 50 } = await req.json();

    const records = await getCreditsByUserUuid(user_uuid, page, limit);

    return respData(records || []);
  } catch (e) {
    console.log("get user credit records failed: ", e);
    return respErr("get user credit records failed");
  }
}
