'use client';

import { useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { toast } from 'sonner';
import { useAppContext } from '@/contexts/app';
import { useTranslations } from 'next-intl';

export default function PaymentSuccessToast() {
  console.log('PaymentSuccessToast: Component rendered');
  const searchParams = useSearchParams();
  const { user, setUser } = useAppContext();
  const t = useTranslations('payment');

  // 刷新用户信息的函数
  const refreshUserInfo = async () => {
    try {
      const resp = await fetch("/api/get-user-info", {
        method: "POST",
      });

      if (!resp.ok) {
        throw new Error("fetch user info failed with status: " + resp.status);
      }

      const { code, message, data } = await resp.json();
      if (code !== 0) {
        throw new Error(message);
      }

      setUser(data);
    } catch (e) {
      console.log("refresh user info failed:", e);
    }
  };

  useEffect(() => {
    console.log('PaymentSuccessToast: useEffect triggered');
    console.log('PaymentSuccessToast: searchParams:', searchParams.toString());

    const payment = searchParams.get('payment');
    const credits = searchParams.get('credits');
    const amount = searchParams.get('amount');
    const orderNo = searchParams.get('order_no');

    console.log('PaymentSuccessToast: URL params:', { payment, credits, amount, orderNo });

    if (payment === 'success') {
      console.log('PaymentSuccessToast: Showing success toast');
      // 显示支付成功提示
      const message = t('success.thank_you_message');

      toast.success(message, {
        duration: 8000,
        description: t('success.credits_description', { credits: credits || '0' }),
        action: {
          label: t('success.view_user_center'),
          onClick: () => {
            // 跳转到用户中心
            window.location.href = '/i/user-center';
          }
        }
      });

      // 刷新用户信息以更新积分显示
      refreshUserInfo();

      // 清理URL参数，避免刷新页面时重复显示
      const url = new URL(window.location.href);
      url.searchParams.delete('payment');
      url.searchParams.delete('credits');
      url.searchParams.delete('amount');
      url.searchParams.delete('order_no');
      window.history.replaceState({}, '', url.toString());
    }
  }, [searchParams, t]);

  return null;
}
