'use client';

import React, { useRef, useState } from "react";
//修改：新增依赖
import { useSession, signIn } from "next-auth/react";
import { useTranslations } from "next-intl";


export default function HomePage() {
  const t = useTranslations('homepage');
  // 新增：获取当前登录状态
  const { data: session } = useSession();

  // 本地图片上传与预览、处理状态
  const [file, setFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [processing, setProcessing] = useState(false);
  const [resultUrl, setResultUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 获取翻译数据
  const features = t.raw('features') as Array<{title: string, description: string}>;
  const steps = t.raw('steps') as Array<{title: string, description: string}>;
  const testimonials = t.raw('testimonials') as Array<{name: string, role: string, content: string}>;
  const quickFeatures = t.raw('quick_features') as string[];

  // 添加图标映射
  const featureIcons = ["⚡️", "⏱️", "🔒", "🖼️", "📱", "✅"];

  // 触发文件选择（新增：加入登陆校验功能）
  const triggerFileInput = () => {
    if (!session?.user) {
      alert(t('login_required')); // 立即弹窗提示
      signIn("google", { callbackUrl: "/" }); // 登录后跳转首页，避免404
      return;
    }
    fileInputRef.current?.click();
  };

  // 处理文件选择
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const f = e.target.files?.[0];
    if (f) {
      setFile(f);
      setPreviewUrl(URL.createObjectURL(f));
      setResultUrl(null);
      setError(null);
    }
  };

  // 重置图片
  const handleReset = () => {
    setFile(null);
    setPreviewUrl(null);
    setResultUrl(null);
    setError(null);
  };

  // 去水印处理
  const handleRemoveWatermark = async () => {
    if (!file) return;

    // 检查用户是否已登录
    if (!session?.user) {
      alert(t('login_required'));
      signIn("google", { callbackUrl: "/" });
      return;
    }

    setProcessing(true);
    setError(null);
    setResultUrl(null);
    try {
      const formData = new FormData();
      formData.append("file", file);
      const res = await fetch("/api/remove-watermark", {
        method: "POST",
        body: formData,
      });
      const data = await res.json();
      if (res.status === 402) {
        // 积分不足
        setError(t('insufficient_credits', {
          current: data.current_credits,
          required: data.required_credits
        }));
      } else if (data.image) {
        setResultUrl(`data:image/png;base64,${data.image}`);
      } else {
        setError(data.error || t('processing_failed'));
      }
    } catch (e) {
      setError(t('network_error'));
    } finally {
      setProcessing(false);
    }
  };

  // 下载处理后图片
  const handleDownload = () => {
    if (resultUrl) {
      const a = document.createElement("a");
      a.href = resultUrl;
      a.download = file?.name?.replace(/\.(\w+)$/, "_no_watermark.png") || "result.png";
      a.click();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex flex-col items-center">
      {/* 顶部导航 */}
      {/* 主内容区 */}
      <main className="flex-1 w-full flex flex-col items-center justify-center pt-32 pb-12 px-4">
        {/* 大标题区 */}
        <section className="w-full max-w-3xl flex flex-col items-center text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-extrabold mb-4 text-white drop-shadow">{t('title')}</h1>
          <p className="text-lg md:text-xl text-gray-300 mb-6">{t('description')}</p>
          <div
            className="bg-white/90 rounded-2xl shadow-xl p-8 flex flex-col items-center w-full max-w-md border border-blue-100 mb-4 cursor-pointer hover:shadow-2xl transition group"
            onClick={triggerFileInput}
          >
            {!file ? (
              <>
                <button
                  className="w-full h-48 border-4 border-dashed border-blue-200 dark:border-blue-400/50 rounded-2xl flex flex-col items-center justify-center text-blue-400 dark:text-blue-300 hover:border-blue-400 dark:hover:border-blue-300 transition mb-4 bg-gradient-to-br from-blue-100 via-pink-100 to-purple-100 dark:from-blue-900/20 dark:via-purple-900/20 dark:to-blue-900/20 group-hover:scale-105 group-hover:shadow-lg dark:shadow-blue-500/20"
                  onClick={e => { e.stopPropagation(); triggerFileInput(); }}
                >
                  <div className="flex flex-col items-center">
                    <div className="w-14 h-14 rounded-full bg-gradient-to-br from-blue-400 via-pink-400 to-purple-500 flex items-center justify-center mb-2 shadow-lg group-hover:scale-110 transition-transform">
                      <svg width="32" height="32" fill="none" viewBox="0 0 24 24"><path fill="#fff" d="M12 3v12.586l4.95-4.95 1.414 1.414L12 19.414l-6.364-6.364 1.414-1.414 4.95 4.95V3h2z"/></svg>
                    </div>
                    <span className="text-xl font-semibold">{t('upload_text')}</span>
                    <span className="text-xs mt-2 text-blue-400">{t('supported_formats')}</span>
                  </div>
                </button>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  className="hidden"
                  onChange={handleFileChange}
                />
              </>
            ) : (
              <div className="flex flex-col items-center w-full">
                <img
                  src={previewUrl!}
                  alt="预览"
                  className="max-h-64 rounded-xl shadow-lg mb-2 border-2 border-blue-200 bg-white animate-fade-in"
                  style={{ maxWidth: "100%" }}
                />
                <div className="text-sm text-gray-500 mb-2">{file.name} ({(file.size / 1024 / 1024).toFixed(2)} MB)</div>
                {/* 新增：去水印按钮、处理结果、下载、错误提示 */}
                {error && <div className="text-red-500 text-sm mb-2">{error}</div>}
                {!resultUrl && (
                  <button
                    className="w-full py-3 px-4 bg-gradient-to-r from-blue-500 via-pink-400 to-purple-500 text-white rounded-xl font-semibold hover:from-blue-600 hover:to-purple-700 transition mb-2 shadow-lg text-lg disabled:opacity-60 disabled:cursor-not-allowed"
                    onClick={handleRemoveWatermark}
                    disabled={processing}
                  >
                    {processing ? (
                      <span className="flex items-center justify-center gap-2"><span className="loader border-white border-t-blue-400"></span> {t('processing')}</span>
                    ) : (
                      t('remove_watermark')
                    )}
                  </button>
                )}
                {resultUrl && (
                  <>
                    <div className="mb-4 w-full flex flex-col items-center">
                      <img
                        src={resultUrl}
                        alt="去水印结果"
                        className="max-h-64 rounded-xl shadow-lg mb-2 border-2 border-green-400 bg-white animate-fade-in"
                        style={{ maxWidth: "100%" }}
                      />
                    </div>
                    <button
                      className="w-full py-3 px-4 bg-gradient-to-r from-green-500 via-blue-400 to-purple-500 text-white rounded-xl font-semibold hover:from-green-600 hover:to-purple-700 transition mb-2 shadow-lg text-lg"
                      onClick={handleDownload}
                    >
                      {t('download')}
                    </button>
                  </>
                )}
                <button
                  className="w-full py-2 px-4 bg-gray-100 text-gray-700 rounded-xl font-semibold hover:bg-gray-200 transition shadow mt-2"
                  onClick={handleReset}
                  disabled={processing}
                >{t('reselect_image')}</button>
              </div>
            )}
          </div>
          <div className="flex gap-4 mt-2 text-xs text-gray-300">
            {quickFeatures.map((feature, i) => (
              <span key={i}>✔ {feature}</span>
            ))}
          </div>
        </section>
        {/* AI特性区块 */}
        <section id="features" className="w-full max-w-5xl mb-16">
          <h2 className="text-2xl font-bold text-white mb-6 text-center">{t('features_title')}</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {features.map((f, i) => (
              <div key={i} className="bg-white rounded-2xl shadow p-6 flex flex-col items-center text-center border border-blue-100">
                <div className="text-3xl mb-2">{featureIcons[i]}</div>
                <div className="font-bold text-blue-700 mb-1">{f.title}</div>
                <div className="text-gray-500 text-sm">{f.description}</div>
              </div>
            ))}
          </div>
        </section>
        {/* 使用流程区块 */}
        <section id="how" className="w-full max-w-4xl mb-16">
          <h2 className="text-2xl font-bold text-white mb-6 text-center">{t('steps_title')}</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {steps.map((s, i) => (
              <div key={i} className="bg-gradient-to-br from-blue-50 via-pink-50 to-purple-100 rounded-2xl shadow p-6 flex flex-col items-center text-center border border-blue-100">
                <div className="w-10 h-10 rounded-full bg-blue-500 text-white flex items-center justify-center font-bold text-lg mb-2">{i + 1}</div>
                <div className="font-bold text-blue-700 mb-1">{s.title}</div>
                <div className="text-gray-500 text-sm">{s.description}</div>
              </div>
            ))}
          </div>
        </section>
        {/* 用户评价区块 */}
        <section id="testimonials" className="w-full max-w-4xl mb-20">
          <h2 className="text-2xl font-bold text-white mb-6 text-center">{t('testimonials_title')}</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, i) => (
              <div key={i} className="bg-white rounded-2xl shadow p-6 flex flex-col items-center text-center border border-blue-100">
                <img src={`/imgs/users/${i + 1}.png`} alt={testimonial.name} className="w-14 h-14 rounded-full mb-2 border-2 border-blue-200 object-cover" />
                <div className="font-bold text-blue-700 mb-1">{testimonial.name}</div>
                <div className="text-xs text-gray-400 mb-1">{testimonial.role}</div>
                <div className="text-gray-500 text-sm">{testimonial.content}</div>
              </div>
            ))}
          </div>
        </section>
      </main>

      {/* 动画样式 */}
      <style jsx global>{`
        @keyframes fade-in {
          from { opacity: 0; transform: scale(0.98); }
          to { opacity: 1; transform: scale(1); }
        }
        .animate-fade-in {
          animation: fade-in 0.5s ease;
        }
        .loader {
          border: 3px solid #fff;
          border-radius: 50%;
          border-top: 3px solid #60a5fa;
          width: 1.2em;
          height: 1.2em;
          animation: spin 0.8s linear infinite;
        }
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
}
