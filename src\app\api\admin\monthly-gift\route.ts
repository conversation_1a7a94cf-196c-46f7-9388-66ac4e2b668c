import { respData, respErr } from "@/lib/resp";
import { isCurrentUserAdmin } from "@/services/admin";
import { giveMonthlyGiftToSubscriptionUsers } from "@/services/credit";

/**
 * 管理员手动触发月度积分赠送
 */
export async function POST(req: Request) {
  try {
    // 检查管理员权限
    const isAdmin = await isCurrentUserAdmin();
    if (!isAdmin) {
      return respErr("无权限访问", 403);
    }

    console.log("Admin triggered monthly gift distribution");
    
    // 执行月度积分赠送
    await giveMonthlyGiftToSubscriptionUsers();

    return respData({ message: "月度积分赠送完成" });
  } catch (e) {
    console.log("monthly gift distribution failed: ", e);
    return respErr("月度积分赠送失败");
  }
}
