"use client";

import * as React from "react";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

import { Link } from "@/i18n/navigation";
import { User } from "@/types/user";
import { signOut } from "next-auth/react";
import { useTranslations } from "next-intl";
import { NavItem } from "@/types/blocks/base";
import { ChevronDown } from "lucide-react";


export default function SignUser({ user }: { user: User }) {
  const t = useTranslations();

  const dropdownItems: NavItem[] = [
    {
      title: user.nickname,
    },
    {
      title: t("user.user_center"),
      url: "/i/user-center",
    },
    {
      title: t("user.admin_system"),
      url: "/admin",
    },
    {
      title: t("contact.title"),
      url: "/contact",
    },
    {
      title: t("user.sign_out"),
      onClick: () => signOut(),
    },
  ];

  return (
    <TooltipProvider>
      <DropdownMenu>
        <Tooltip>
          <TooltipTrigger asChild>
            <DropdownMenuTrigger asChild>
              <div className="flex items-center gap-2 cursor-pointer hover:bg-accent hover:text-accent-foreground rounded-md px-2 py-1 transition-colors">
                <Avatar className="h-8 w-8 shrink-0">
                  <AvatarImage src={user.avatar_url} alt={user.nickname} />
                  <AvatarFallback className="text-xs font-medium">
                    {user.nickname?.slice(0, 2).toUpperCase() || 'UN'}
                  </AvatarFallback>
                </Avatar>
                <div className="hidden sm:flex items-center gap-1 min-w-0">
                  <span className="text-sm font-medium truncate max-w-[100px] md:max-w-[120px] lg:max-w-[140px]">
                    {user.nickname}
                  </span>
                  <ChevronDown className="h-3 w-3 opacity-50 shrink-0" />
                </div>
                {/* 移动端只显示头像，悬停时显示下拉箭头 */}
                <div className="sm:hidden">
                  <ChevronDown className="h-3 w-3 opacity-50" />
                </div>
              </div>
            </DropdownMenuTrigger>
          </TooltipTrigger>
          <TooltipContent>
            <p>{user.nickname}</p>
          </TooltipContent>
        </Tooltip>
      <DropdownMenuContent className="mx-4 bg-background">
        {dropdownItems.map((item, index) => (
          <React.Fragment key={index}>
            <DropdownMenuItem
              key={index}
              className="flex justify-center cursor-pointer"
            >
              {item.url ? (
                <Link href={item.url as any} target={item.target}>
                  {item.title}
                </Link>
              ) : (
                <button onClick={item.onClick}>{item.title}</button>
              )}
            </DropdownMenuItem>
            {index !== dropdownItems.length - 1 && <DropdownMenuSeparator />}
          </React.Fragment>
        ))}
      </DropdownMenuContent>
      </DropdownMenu>
    </TooltipProvider>
  );
}
