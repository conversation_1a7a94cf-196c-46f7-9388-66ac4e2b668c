"use client";
import React, { useState } from "react";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { useAppContext } from "@/contexts/app";
import { useRouter, useParams } from "next/navigation";
import { useEffect } from "react";

import { useTranslations } from "next-intl";
import { useSession, signIn } from "next-auth/react";
import { toast } from "sonner";
import { useSearchParams } from "next/navigation";

interface CreditRecord {
  id: number;
  trans_no: string;
  created_at: string;
  trans_type: string;
  credits: number;
  order_no?: string;
  expired_at?: string;
}

// 交易类型显示映射
const getTransTypeDisplay = (transType: string, t: any): string => {
  const typeMap: { [key: string]: string } = {
    'new_user': t('user_center.trans_types.new_user'),
    'order_pay': t('user_center.trans_types.order_pay'),
    'system_add': t('user_center.trans_types.system_add'),
    'monthly_gift': t('user_center.trans_types.monthly_gift'),
    'ping': t('user_center.trans_types.ping'),
    'remove_watermark': t('user_center.trans_types.remove_watermark'),
  };
  return typeMap[transType] || transType;
};

// 获取账单用途显示名称
const getBillPurposeDisplay = (purpose: string, t: any): string => {
  const purposeMap: { [key: string]: string } = {
    'credits_purchase': t('user_center.bill_purposes.credits_purchase'),
    'subscription_monthly': t('user_center.bill_purposes.subscription_monthly'),
    'subscription_yearly': t('user_center.bill_purposes.subscription_yearly'),
    'one_time_purchase': t('user_center.bill_purposes.one_time_purchase'),
  };
  return purposeMap[purpose] || purpose;
};

// 获取支付方式显示名称
const getPaymentMethodDisplay = (paymentMethod: string, t: any): string => {
  const methodMap: { [key: string]: string } = {
    'paypal': 'PayPal',
    'stripe': 'Stripe',
    'alipay': t('user_center.payment_methods.alipay'),
    'wechat': t('user_center.payment_methods.wechat'),
  };
  return methodMap[paymentMethod?.toLowerCase()] || paymentMethod || t('user_center.payment_methods.unknown');
};

export default function UserCenterPage() {
  const [tab, setTab] = useState<'log' | 'bills' | 'info'>("log");
  const [creditRecords, setCreditRecords] = useState<CreditRecord[]>([]);
  const [billRecords, setBillRecords] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [billsLoading, setBillsLoading] = useState(false);
  const [initialized, setInitialized] = useState(false);
  const { user, setUser } = useAppContext();
  const { data: session, status } = useSession();
  const router = useRouter();
  const params = useParams();

  // 支付成功提示
  const searchParams = useSearchParams();
  const paymentT = useTranslations('payment');
  const locale = params.locale as string;
  const t = useTranslations();

  // 刷新用户信息的函数
  const refreshUserInfo = async () => {
    try {
      const resp = await fetch("/api/get-user-info", {
        method: "POST",
      });

      if (!resp.ok) {
        throw new Error("fetch user info failed with status: " + resp.status);
      }

      const { code, message, data } = await resp.json();
      if (code !== 0) {
        throw new Error(message);
      }

      setUser(data);
    } catch (e) {
      console.log("refresh user info failed:", e);
    }
  };

  // 获取积分消费记录
  const fetchCreditRecords = async () => {
    if (!user) return;

    setLoading(true);
    try {
      const response = await fetch('/api/get-user-credit-records', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ page: 1, limit: 100 }),
      });

      if (response.ok) {
        const { code, data } = await response.json();
        if (code === 0) {
          setCreditRecords(data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch credit records:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取账单记录
  const fetchBillRecords = async () => {
    if (!user) return;

    setBillsLoading(true);
    try {
      const response = await fetch('/api/get-user-bills', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ page: 1, limit: 100 }),
      });

      if (response.ok) {
        const { code, data } = await response.json();
        if (code === 0) {
          setBillRecords(data);
        }
      }
    } catch (error) {
      console.error('Failed to fetch bill records:', error);
    } finally {
      setBillsLoading(false);
    }
  };

  // 检查用户是否已登录，如果未登录则重定向到登录页面
  useEffect(() => {
    if (status === "loading") return; // 等待session加载完成

    if (status === "unauthenticated") {
      // 用户未认证，重定向到登录页面
      const callbackUrl = `/${locale}/i/user-center`;
      router.push(`/${locale}/auth/signin?callbackUrl=${encodeURIComponent(callbackUrl)}`);
      return;
    }

    if (status === "authenticated" && session?.user && !initialized) {
      // 用户已认证且未初始化，获取数据
      refreshUserInfo();
      fetchCreditRecords();
      fetchBillRecords();
      setInitialized(true);
    }
  }, [status, session, router, initialized, locale]);

  // 支付成功提示
  useEffect(() => {
    const payment = searchParams.get('payment');
    const credits = searchParams.get('credits');

    if (payment === 'success') {
      // 显示支付成功提示
      const message = paymentT('success.thank_you_message');

      toast.success(message, {
        duration: 8000,
        description: paymentT('success.credits_description', { credits: credits || '0' }),
        action: {
          label: paymentT('success.view_user_center'),
          onClick: () => {
            // 刷新页面
            window.location.reload();
          }
        }
      });

      // 清理URL参数，避免刷新页面时重复显示
      const url = new URL(window.location.href);
      url.searchParams.delete('payment');
      url.searchParams.delete('credits');
      url.searchParams.delete('amount');
      url.searchParams.delete('order_no');
      window.history.replaceState({}, '', url.toString());
    }
  }, [searchParams, paymentT]);

  // 如果session正在加载或用户未登录，显示加载状态
  if (status === "loading" || status === "unauthenticated" || !user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-pink-50 to-purple-50 flex flex-col items-center justify-center">
        <div className="text-lg">{t('user_center.loading')}</div>
      </div>
    );
  }

  return (
    <>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-pink-50 to-purple-50 flex flex-col items-center py-16">
      {/* 如需调整白色卡片宽度，可修改max-w-7xl为max-w-6xl、max-w-4xl等，或直接用w-[1200px]自定义 */}
      <div className="w-full max-w-7xl bg-white rounded-xl shadow-2xl p-12 flex flex-col md:flex-row gap-12">
        {/* 左侧：头像+按钮 */}
        <div className="flex flex-col items-center md:items-start min-w-[200px]">
          <div className="flex items-center gap-6 mb-8">
            <Avatar className="w-20 h-20">
              <AvatarImage src={user.avatar_url} alt={user.nickname} />
              <AvatarFallback>{user.nickname?.charAt(0) || 'U'}</AvatarFallback>
            </Avatar>
            <div className="flex flex-col">
              <span className="text-gray-500 text-base">{t('user_center.remaining_credits')}</span>
              <span className="text-3xl font-bold text-green-600">{user.credits?.left_credits || 0}</span>
            </div>
          </div>
          <Button
            className={`w-40 mb-4 ${tab === "log" ? "bg-blue-500 text-white" : "bg-gray-100 text-gray-700"}`}
            onClick={() => {
              setTab("log");
              // 只在切换到该标签时获取数据，如果已有数据则不重复获取
              if (creditRecords.length === 0) {
                fetchCreditRecords();
              }
            }}
            variant="ghost"
            size="lg"
          >
            {t('user_center.credit_details')}
          </Button>
          <Button
            className={`w-40 mb-4 ${tab === "bills" ? "bg-blue-500 text-white" : "bg-gray-100 text-gray-700"}`}
            onClick={() => {
              setTab("bills");
              // 只在切换到该标签时获取数据，如果已有数据则不重复获取
              if (billRecords.length === 0) {
                fetchBillRecords();
              }
            }}
            variant="ghost"
            size="lg"
          >
            {t('user_center.bill_records')}
          </Button>
          <Button
            className={`w-40 ${tab === "info" ? "bg-blue-500 text-white" : "bg-gray-100 text-gray-700"}`}
            onClick={() => {
              setTab("info");
              // 账户信息标签不需要额外的数据获取
            }}
            variant="ghost"
            size="lg"
          >
            {t('user_center.account_info')}
          </Button>
        </div>
        {/* 右侧内容区 */}
        <div className="flex-1 min-w-0">
          {tab === "log" ? (
            <div>
              <div className="text-2xl font-bold mb-6">{t('user_center.credit_details')}</div>
              <div className="h-96 overflow-y-auto pr-2 border rounded-2xl bg-blue-50/40 shadow-inner">
                {/* 表头 */}
                <div className="grid grid-cols-4 font-semibold text-blue-700 bg-blue-100 py-3 px-2 sticky top-0 z-10 rounded-t-2xl border-b border-blue-200">
                  <div className="text-center">{t('user_center.transaction_time')}</div>
                  <div className="text-center">{t('user_center.transaction_type')}</div>
                  <div className="text-center">{t('user_center.credit_change')}</div>
                  <div className="text-center">{t('user_center.transaction_id')}</div>
                </div>
                {loading ? (
                  <div className="flex justify-center items-center py-8">
                    <div className="text-gray-500">{t('user_center.loading')}</div>
                  </div>
                ) : (
                  <ul className="divide-y divide-blue-100">
                    {creditRecords.length > 0 ? (
                      creditRecords.map((record) => (
                        <li key={record.id} className="grid grid-cols-4 items-center py-3 px-2">
                          <div className="text-gray-600 text-center text-sm">
                            {new Date(record.created_at).toLocaleString('zh-CN')}
                          </div>
                          <div className="text-gray-700 text-center text-sm">
                            {getTransTypeDisplay(record.trans_type, t)}
                          </div>
                          <div className={`font-bold text-center text-sm ${
                            record.credits > 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {record.credits > 0 ? '+' : ''}{record.credits}
                          </div>
                          <div className="text-gray-500 text-center text-xs">
                            {record.trans_no.slice(-8)}
                          </div>
                        </li>
                      ))
                    ) : (
                      <li className="py-8 text-center text-gray-500">
                        {t('user_center.no_records')}
                      </li>
                    )}
                  </ul>
                )}
              </div>
            </div>
          ) : tab === "bills" ? (
            <div>
              <div className="text-2xl font-bold mb-6">{t('user_center.bill_records')}</div>
              <div className="h-96 overflow-y-auto pr-2 border rounded-2xl bg-green-50/40 shadow-inner">
                {/* 表头 */}
                <div className="grid grid-cols-6 font-semibold text-green-700 bg-green-100 py-3 px-2 sticky top-0 z-10 rounded-t-2xl border-b border-green-200">
                  <div className="text-center">{t('user_center.bill_headers.transaction_time')}</div>
                  <div className="text-center">{t('user_center.bill_headers.transaction_type')}</div>
                  <div className="text-center">{t('user_center.bill_headers.purpose')}</div>
                  <div className="text-center">{t('user_center.bill_headers.payment_method')}</div>
                  <div className="text-center">{t('user_center.bill_headers.amount')}</div>
                  <div className="text-center">{t('user_center.bill_headers.credits')}</div>
                </div>
                {billsLoading ? (
                  <div className="flex justify-center items-center py-8">
                    <div className="text-gray-500">{t('user_center.loading')}</div>
                  </div>
                ) : (
                  <ul className="divide-y divide-green-100">
                    {billRecords.length > 0 ? (
                      billRecords.map((record) => (
                        <li key={record.id} className="grid grid-cols-6 items-center py-3 px-2">
                          <div className="text-gray-600 text-center text-sm">
                            {new Date(record.created_at).toLocaleString('zh-CN')}
                          </div>
                          <div className="text-gray-700 text-center text-sm">
                            {record.transaction_type === 'payment' ? t('user_center.bill_headers.transaction_type') : record.transaction_type}
                          </div>
                          <div className="text-gray-700 text-center text-sm">
                            {getBillPurposeDisplay(record.purpose, t)}
                          </div>
                          <div className="text-gray-700 text-center text-sm">
                            {getPaymentMethodDisplay(record.payment_method, t)}
                          </div>
                          <div className="text-gray-700 text-center text-sm font-medium">
                            {record.currency?.toUpperCase()} {(record.amount / 100).toFixed(2)}
                          </div>
                          <div className="text-green-600 text-center text-sm font-bold">
                            +{record.credits || 0}
                          </div>
                        </li>
                      ))
                    ) : (
                      <li className="py-8 text-center text-gray-500">
                        {t('user_center.no_bills')}
                      </li>
                    )}
                  </ul>
                )}
              </div>
            </div>
          ) : (
            <div>
              <div className="text-2xl font-bold mb-6">{t('user_center.account_info')}</div>
              <div className="bg-gray-50 rounded-2xl p-8 flex flex-col gap-6 w-full max-w-lg shadow">
                <div className="flex gap-6 items-center">
                  <Avatar className="w-16 h-16">
                    <AvatarImage src={user.avatar_url} alt={user.nickname} />
                    <AvatarFallback>{user.nickname?.charAt(0) || 'U'}</AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-bold text-xl">{user.nickname}</div>
                    <div className="text-gray-500 text-base">{t('user_center.account.nickname')}</div>
                  </div>
                </div>
                <div className="flex flex-col gap-2 mt-2">
                  <div className="text-gray-700 text-base"><span className="font-semibold">{t('user_center.account.id')}：</span>{user.uuid}</div>
                  <div className="text-gray-700 text-base"><span className="font-semibold">{t('user_center.account.email')}：</span>{user.email}</div>
                </div>

                {/* Payment Method Info */}
                <div className="border-t pt-4 mt-4">
                  <div className="text-lg font-semibold mb-3">{t('user_center.account.payment_method')}</div>
                  <div className="bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-lg p-4 border border-blue-200">
                    <div className="flex items-center gap-3 mb-2">
                      <img src="/imgs/logos/paypal.png" alt="PayPal" className="h-6" />
                      <span className="font-medium">PayPal</span>
                      <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">{t('user_center.account.paypal_enabled')}</span>
                    </div>
                    <p className="text-sm text-gray-600">
                      {t('user_center.account.paypal_description')}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
    </>
  );
}