import { getTranslations } from "next-intl/server";

export default async function PrivacyPolicyPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const t = await getTranslations({ locale, namespace: 'legal.privacy' });

  return (
    <div className="prose prose-slate dark:prose-invert max-w-none">
      <h1 className="text-4xl font-bold mb-8 text-center bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
        {t('title')}
      </h1>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-4 text-blue-600 dark:text-blue-400">
          {t('introduction.title')}
        </h2>
        <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
          {t('introduction.content')}
        </p>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-4 text-blue-600 dark:text-blue-400">
          {t('information_collection.title')}
        </h2>
        <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
          {t('information_collection.intro')}
        </p>
        
        <div className="space-y-6">
          <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <h3 className="text-lg font-semibold mb-2 text-blue-800 dark:text-blue-300">
              {t('information_collection.account.title')}
            </h3>
            <ul className="list-disc list-inside space-y-1 text-gray-700 dark:text-gray-300">
              <li><strong>{t('information_collection.account.what_we_collect')}:</strong> {t('information_collection.account.details')}</li>
              <li><strong>{t('information_collection.account.purpose')}:</strong> {t('information_collection.account.purpose_details')}</li>
            </ul>
          </div>

          <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
            <h3 className="text-lg font-semibold mb-2 text-green-800 dark:text-green-300">
              {t('information_collection.image_processing.title')}
            </h3>
            <ul className="list-disc list-inside space-y-1 text-gray-700 dark:text-gray-300">
              <li><strong>{t('information_collection.image_processing.what_we_collect')}:</strong> {t('information_collection.image_processing.details')}</li>
              <li><strong>{t('information_collection.image_processing.purpose')}:</strong> {t('information_collection.image_processing.purpose_details')}</li>
              <li><strong>{t('information_collection.image_processing.retention')}:</strong> {t('information_collection.image_processing.retention_details')}</li>
            </ul>
          </div>

          <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
            <h3 className="text-lg font-semibold mb-2 text-purple-800 dark:text-purple-300">
              {t('information_collection.usage_details.title')}
            </h3>
            <ul className="list-disc list-inside space-y-1 text-gray-700 dark:text-gray-300">
              <li><strong>{t('information_collection.usage_details.what_we_collect')}:</strong> {t('information_collection.usage_details.details')}</li>
              <li><strong>{t('information_collection.usage_details.purpose')}:</strong> {t('information_collection.usage_details.purpose_details')}</li>
            </ul>
          </div>
        </div>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-4 text-blue-600 dark:text-blue-400">
          {t('data_security.title')}
        </h2>
        <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
          {t('data_security.content')}
        </p>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-4 text-blue-600 dark:text-blue-400">
          {t('information_sharing.title')}
        </h2>
        <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
          {t('information_sharing.intro')}
        </p>
        <ul className="list-disc list-inside space-y-2 text-gray-700 dark:text-gray-300">
          <li>{t('information_sharing.legal_compliance')}</li>
          <li>{t('information_sharing.protection')}</li>
          <li>{t('information_sharing.third_party')}</li>
        </ul>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-4 text-blue-600 dark:text-blue-400">
          {t('policy_changes.title')}
        </h2>
        <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
          {t('policy_changes.content')}
        </p>
      </section>

      <section className="mb-8">
        <h2 className="text-2xl font-semibold mb-4 text-blue-600 dark:text-blue-400">
          {t('contact.title')}
        </h2>
        <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-4">
          {t('contact.intro')}
        </p>
        <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg">
          <p className="text-gray-700 dark:text-gray-300">
            <strong>{t('contact.service_provider')}:</strong> Watermark Remover<br />
            <strong>{t('contact.email')}:</strong> <EMAIL>
          </p>
        </div>
        <p className="text-gray-700 dark:text-gray-300 leading-relaxed mt-4">
          {t('contact.consent')}
        </p>
      </section>
    </div>
  );
}
